#!/usr/bin/env python3
"""
Test script to verify the HR search fix for Tekion.com
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from services.email_finder import EmailFinder

def test_tekion_hr_search():
    """Test HR search for Tekion specifically"""
    print("🧪 Testing HR search for Tekion.com...")
    print("=" * 60)
    
    finder = EmailFinder()
    
    # Test with Tekion
    company_name = "Tekion"
    company_domain = "tekion.com"
    
    print(f"Searching for HR contacts at {company_name} ({company_domain})")
    print("-" * 60)
    
    try:
        results = finder.find_hr_emails(company_name, company_domain)
        
        print(f"\n📊 RESULTS SUMMARY:")
        print(f"Total contacts found: {len(results)}")
        print("=" * 60)
        
        for i, contact in enumerate(results, 1):
            print(f"\n{i}. {contact['name']}")
            print(f"   📧 Email: {contact['email']}")
            print(f"   💼 Position: {contact.get('position', 'N/A')}")
            print(f"   🏢 Source: {contact.get('source', 'N/A')}")
            print(f"   📍 Location: {contact.get('location', 'N/A')}")
            print(f"   🎯 Confidence: {contact.get('confidence', 'N/A')}%")
            
            # Check if this is actually from Tekion
            email_domain = contact['email'].split('@')[-1] if '@' in contact['email'] else 'unknown'
            if email_domain == company_domain:
                print(f"   ✅ Correct domain: {email_domain}")
            else:
                print(f"   ❌ Wrong domain: {email_domain} (expected: {company_domain})")
                
            if contact.get('alternative_emails'):
                print(f"   🔄 Alternatives: {', '.join(contact['alternative_emails'][:2])}")
        
        # Analyze results
        print(f"\n📈 ANALYSIS:")
        correct_domain_count = sum(1 for c in results if company_domain in c['email'])
        print(f"Contacts with correct domain ({company_domain}): {correct_domain_count}/{len(results)}")
        
        real_people_count = sum(1 for c in results if 'Real Person' in c.get('source', ''))
        print(f"Real people found: {real_people_count}/{len(results)}")
        
        india_based_count = sum(1 for c in results if c.get('location') == 'India')
        print(f"India-based contacts: {india_based_count}/{len(results)}")
        
    except Exception as e:
        print(f"❌ Error during search: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tekion_hr_search()
