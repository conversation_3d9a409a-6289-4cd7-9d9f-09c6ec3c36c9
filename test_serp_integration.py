#!/usr/bin/env python3
"""
Test script for SERP API HR finder integration
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.serp_hr_finder import SerpHR<PERSON><PERSON>

def test_serp_hr_finder():
    """Test the SERP API HR finder with a sample company"""
    
    print("🧪 Testing SERP API HR Finder Integration")
    print("=" * 50)
    
    # Test companies
    test_companies = [
        {
            'name': 'Google',
            'domain': 'google.com'
        },
        {
            'name': 'Microsoft',
            'domain': 'microsoft.com'
        },
        {
            'name': 'Infosys',
            'domain': 'infosys.com'
        }
    ]
    
    serp_finder = SerpHRFinder()
    
    for company in test_companies:
        print(f"\n🏢 Testing: {company['name']} ({company['domain']})")
        print("-" * 40)
        
        try:
            results = serp_finder.find_hr_professionals(
                company['name'], 
                company['domain']
            )
            
            if results:
                print(f"✅ Found {len(results)} HR professionals")
                
                for i, contact in enumerate(results[:3], 1):  # Show top 3
                    print(f"\n  {i}. {contact['name']}")
                    print(f"     📧 {contact['email']}")
                    print(f"     💼 {contact['position']}")
                    print(f"     📍 {contact.get('location', 'Unknown')}")
                    print(f"     🔍 {contact['source']}")
                    print(f"     📊 Confidence: {contact['confidence']}%")
                    
                    if contact.get('linkedin_url'):
                        print(f"     🔗 LinkedIn: {contact['linkedin_url']}")
                    
                    if contact.get('alternative_emails'):
                        print(f"     📧 Alt emails: {', '.join(contact['alternative_emails'][:2])}")
            else:
                print("❌ No HR professionals found")
                
        except Exception as e:
            print(f"❌ Error testing {company['name']}: {e}")
        
        print("\n" + "="*50)

def test_api_key():
    """Test if SERP API key is configured"""
    print("🔑 Testing SERP API Key Configuration")
    print("-" * 40)
    
    from config import Config
    
    if hasattr(Config, 'SERP_API_KEY') and Config.SERP_API_KEY:
        print(f"✅ SERP API Key configured: {Config.SERP_API_KEY[:10]}...")
        return True
    else:
        print("❌ SERP API Key not configured")
        return False

def main():
    """Main test function"""
    print("🚀 SERP API Integration Test Suite")
    print("=" * 50)
    
    # Test API key configuration
    if not test_api_key():
        print("\n❌ Cannot proceed without SERP API key")
        return
    
    print("\n")
    
    # Test SERP HR finder
    test_serp_hr_finder()
    
    print("\n🎉 Test completed!")
    print("\nNext steps:")
    print("1. Run the Flask app: python app.py")
    print("2. Navigate to the search page")
    print("3. Try the 'SERP API (India)' button")
    print("4. Check for India-based HR professionals")

if __name__ == "__main__":
    main()
